<form action="/submit-contact" method="POST"
    class="col-span-2 self-stretch p-12 bg-stone-950 rounded-xl outline outline-1 outline-offset-[-1px] outline-neutral-800 flex flex-col justify-start items-start gap-12 w-full max-w-5xl mx-auto">

    <!-- First Name & Last Name -->
    <div class="self-stretch flex justify-start items-start gap-12 w-full flex-wrap">
        <?php renderInputField("First Name", "Enter First Name", "text", "first_name", "first-name") ?>
        <?php renderInputField("Last Name", "Enter Last Name", "text", "last_name", "last-name") ?>
    </div>

    <!-- Email & Phone Number -->
    <div class="self-stretch flex justify-start items-start gap-12 w-full flex-wrap">
        <?php renderInputField("Email", "Enter your Email", "email", "email", "email") ?>
        <div class="flex-1 flex flex-col justify-start items-start gap-4">
            <label for="phone-number" class="text-white text-lg font-semibold leading-relaxed font-manrope">Phone
                Number</label>
            <div class="self-stretch flex justify-start items-stretch gap-4">
                <?php renderCountryCode() ?>
                <div class="flex-1">
                    <?php renderInputField("", "Enter Phone Number", "tel", "phone", "phone-number") ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Message -->
    <?php renderTextArea("Message", "Enter your Message", "message", "message") ?>

    <!-- Terms & Send Button -->
    <div class="self-stretch flex justify-between items-center gap-16 w-full flex-wrap">
        <?php renderCheckbox("I agree with Terms of Use and Privacy Policy", "terms") ?>
        <?php renderSubmitButton("Send Message") ?>
    </div>

</form>