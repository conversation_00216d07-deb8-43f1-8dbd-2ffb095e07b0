<?php

// Define image paths from the temp folder
$images = [
    'temp/movie1.png',
    'temp/movie2.png',
    'temp/movie3.png',
    'temp/movie2.png',
    'temp/movie1.png'
];

$slides = [
    ['image' => $images[0], 'duration' => '7h 40min', 'views' => '12K', 'has_trending' => true, 'rating' => 4],
    ['image' => $images[1], 'duration' => '12h 23min', 'views' => '28K', 'has_trending' => false, 'rating' => 3],
    ['image' => $images[2], 'duration' => '12h 23min', 'views' => '28K', 'has_trending' => false, 'rating' => 5],
    ['image' => $images[3], 'duration' => '10h 30min', 'views' => '2K', 'has_trending' => false, 'rating' => 2],
    ['image' => $images[4], 'duration' => '8h 20min', 'views' => '32K', 'has_trending' => true, 'rating' => 4],
];

?>

<section id="hero"
    class="relative mt-[-100px] w-full min-h-screen bg-no-repeat tb-sm:bg-cover bg-[size:400%] bg-[position:50%_center]"
    style="background-image: url(<?= IMAGE_SRC ?>hero-bg.webp)">
    <?php include_once __DIR__ . '/mobile/hero.php'; ?>
    <div
        class="max-tb-sm:hidden animate-fade-up animation-delay-300 relative z-10 pt-[200px] flex flex-col justify-center items-center min-h-screen gap-12 bg-gradient-to-b from-zinc-950 via-zinc-950/50 via-30% to-60% to-zinc-950">
        <div>
            <img src="<?= IMAGE_SRC ?>hero.svg" class="backdrop-blur-[6px]">
        </div>
        <div class="flex flex-col justify-start items-center gap-5">
            <div
                class="px-4 py-2 bg-zinc-950/30 rounded-full outline outline-1 outline-offset-[-1px] outline-white backdrop-blur-md flex justify-center items-center gap-4">
                <div class="justify-center text-stone-300 text-sm font-medium font-['Inter'] leading-none">Stream
                    smarter - Laugh louder - Binge better</div>
            </div>
            <div
                class="text-center justify-start text-transparent bg-clip-text bg-gradient-to-r from-white via-50% to-[#767676] text-6xl font-bold font-['Manrope'] leading-[58px] [text-shadow:_0px_25px_50px_rgb(0_0_0_/_0.25)]">
                Unlimited Netflix, Endless Entertainment!</div>
        </div>
        <div
            class="w-[544px] text-center justify-start text-white/70 mb-xl:text-xl text-sm font-medium font-['Inter'] leading-7">
            Instant access to Netflix at unbeatable prices. Stream your favorite movies and series anytime, anywhere.
        </div>
        <a href="<?= CHECKOUT_PAGE ?>"
            class="px-10 py-5 bg-red-600 hover:bg-red-700 rounded-xl shadow-[0px_1px_2px_-1px_rgba(0,0,0,0.10)] flex justify-center items-center gap-2 transition-all duration-300 hover:scale-105">
            <div class="justify-center text-white mb-md:text-lg text-sm font-semibold font-['Inter'] leading-tight">
                Get Instant Access
            </div>
            <?php icon('material-symbols:arrow-forward-rounded', class: 'text-white text-xl'); ?>
        </a>
        <?php include PUBLIC_FOLDER . 'partials/sliders/v1/index.php' ?>
    </div>
</section>