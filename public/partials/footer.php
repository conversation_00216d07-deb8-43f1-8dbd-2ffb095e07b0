<?php

$menuItems = [
    "Quick Links" => [
        ['text' => 'Home', 'link' => '/'],
        ['text' => 'Movies & TV Shows', 'link' => '/movies-tv'],
        ['text' => 'Subscriptions', 'link' => '/subscriptions'],
        ['text' => 'Plans', 'link' => '/subscriptions/plans']
    ],
    "Your Account" => [
        ['text' => 'Sign in', 'link' => '/signin'],
        ['text' => 'Sign up', 'link' => '/signup'],
        ['text' => 'Manage Subscriptions', 'link' => '/account/subscriptions']
    ],
    "Support" => [
        ['text' => 'Contact Us', 'link' => '/support/contact'],
        ['text' => 'FAQs', 'link' => '/support/faqs']
    ],
    "Subscription" => [
        ['text' => 'Plans', 'link' => '/subscriptions/plans'],
        ['text' => 'Features', 'link' => '/subscriptions/features']
    ],
    "Connect With Us" => [
        ['name' => 'facebook', 'icon' => 'ic:baseline-facebook', 'link' => '#'],
        ['name' => 'twitter', 'icon' => 'mdi:twitter', 'link' => '#'],
        ['name' => 'linkedin', 'icon' => 'mdi:linkedin', 'link' => '#']
    ]
];

$footerLinks = [
    ['text' => 'Terms of Use', 'link' => '/terms'],
    ['text' => 'Privacy Policy', 'link' => '/privacy'],
    ['text' => 'Cookie Policy', 'link' => '/cookies']
];

?>

<footer
    class="lp-sm:px-20 tb-lg:px-10 px-4 mt-24 w-full bg-gradient-to-b from-zinc-950 to-zinc-900 flex flex-col justify-start items-start mb-xl:gap-24 gap-12">
    <div class="w-full pt-20 pb-6 flex flex-col justify-start items-start gap-14 border-t custom-border-image">
        <div class="w-full flex gap-20">
            <div class="max-tb-sm:hidden flex flex-col justify-start items-start gap-2.5">
                <a href="/" class="flex justify-start items-center gap-1.5 overflow-hidden">
                    <div class="min-w-[52px]">
                        <img src="<?= IMAGE_SRC ?>logo.svg"></img>
                    </div>
                    <div>
                        <span class="text-2xl font-bold text-white">NetflexCheap</span>
                    </div>
                </a>
                <?php Paragraph("NetflixCheap makes streaming Netflix affordable and effortless, choose flexible monthly or annual plans at unbeatable rates, enjoy instant access with secure payments, and manage your subscriptions all in one place.", "max-w-[400px]"); ?>
            </div>
            <div class="w-full grid grid-cols-2 mb-xl:grid-cols-3 tb-sm:grid-cols-5 gap-4">
                <?php foreach ($menuItems as $title => $items): ?>
                    <div class="flex-1 flex flex-col justify-start items-start gap-6">
                        <span
                            class="self-stretch justify-start text-white mb-xl:text-xl text-sm font-semibold font-['Manrope'] leading-loose">
                            <?= $title ?>
                        </span>

                        <?php if ($title != "Connect With Us"): ?>
                            <div class="flex flex-col justify-start items-start gap-3.5">
                                <?php foreach ($items as $item): ?>
                                    <a href="<?= $item['link'] ?>"
                                        class="text-nowrap self-stretch justify-start text-neutral-400 mb-md:text-lg text-sm font-medium font-['Manrope'] leading-relaxed hover:text-white transition-all duration-300 hover:translate-x-1">
                                        <?= $item['text'] ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="flex justify-start items-start gap-3.5">
                                <?php foreach ($items as $item): ?>
                                    <a href="<?= $item['link'] ?>"
                                        class="p-4 bg-zinc-900 rounded-lg outline outline-1 outline-offset-[-1px] outline-neutral-800 flex justify-start items-start gap-2.5 hover:bg-zinc-800 transition-all duration-300 hover:scale-110">
                                        <?= icon($item['icon'], class: 'text-white mb-md:text-3xl text-xl'); ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <div class="py-6 w-full flex justify-between items-center border-t border-neutral-800 flex-wrap gap-y-4">
        <div class="justify-start text-neutral-400 mb-md:text-lg text-sm font-normal font-['Manrope'] leading-relaxed">
            @2025
            StreamFlix, Made by <a href="https://starthinc.com" target="_blank"
                class="text-neutral-400 hover:text-white">StarthincLabs</a></div>
        <div class="flex justify-between items-start gap-5">
            <?php foreach ($footerLinks as $index => $link): ?>
                <a href="<?= $link['link'] ?>"
                    class="justify-start text-nowrap text-neutral-400 mb-md:text-lg text-sm font-normal font-['Manrope'] leading-relaxed hover:text-white transition-all duration-300 hover:scale-105">
                    <?= $link['text'] ?>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
    </div>