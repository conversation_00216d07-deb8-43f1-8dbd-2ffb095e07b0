import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

import Swiper, { Navigation, Pagination, Autoplay } from 'swiper';

// Initialize movie slider
const movieSlider = new Swiper('.movie-slider', {
    modules: [Autoplay],
    loop: true,
    slidesPerView: 'auto',
    spaceBetween: 20,
    centeredSlides: false,
    autoplay: {
        delay: 1500,
        disableOnInteraction: false,
    },
    breakpoints: {
        640: {
            slidesPerView: 2,
        },
        768: {
            slidesPerView: 3,
        },
        1024: {
            slidesPerView: 4,
        },
    }
});


// single movie page: actors slider
new Swiper('.actors-slider .swiper', {
    modules: [Autoplay, Navigation],
    loop: true,
    slidesPerView: 3,
    spaceBetween: 16,
    centeredSlides: false,
    autoplay: {
        delay: 1500,
        disableOnInteraction: false,
    },
    navigation: {
        nextEl: '.actors-slider .next',
        prevEl: '.actors-slider .prev'
    },
    breakpoints: {
        640: {
            slidesPerView: 4,
        },
        768: {
            slidesPerView: 6,
        },
        1024: {
            slidesPerView: 8,
        },
    }
});