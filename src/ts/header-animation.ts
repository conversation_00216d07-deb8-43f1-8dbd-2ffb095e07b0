document.addEventListener('DOMContentLoaded', () => {
  const header = document.querySelector('header') as HTMLElement;

  if (!header) return;

  const handleScroll = () => {
    const scrollPosition = window.scrollY;

    if (scrollPosition > 0) {
      // When scrolled, add background and shadow
      header.classList.add('bg-zinc-950/80');
      header.classList.add('shadow-[0px_4px_14px_10px_rgba(0,0,0,0.11)]');
      header.classList.remove('bg-transparent');
    } else {
      // At top, make background transparent
      header.classList.remove('bg-zinc-950/80');
      header.classList.remove('shadow-[0px_4px_14px_10px_rgba(0,0,0,0.11)]');
      header.classList.add('bg-transparent');
    }
  };

  // Run once on load
  handleScroll();

  // Add scroll event listener
  window.addEventListener('scroll', handleScroll);
});